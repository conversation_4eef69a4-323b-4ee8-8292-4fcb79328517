import { GoogleGenAI, Type, Part } from '@google/genai';
import { captureApiError } from '@/utils/errorMonitoring';
import { extractTextFromDocx } from '@/utils/docxUtils';
import { StructuredLetterData, validateStructuredLetterData, createDefaultStructuredLetterData } from '@/types/letter-structured';

// Initialize the Google Generative AI client
const googleAI = new GoogleGenAI({apiKey: process.env.NEXT_PUBLIC_GOOGLE_AI_API_KEY || ''});

/**
 * Converts a file to a generative part for the AI model
 */
async function fileToGenerativePart(file: ArrayBuffer, mimeType: string): Promise<Part> {
  const uint8Array = new Uint8Array(file);
  return {
    inlineData: {
      data: Buffer.from(uint8Array).toString('base64'),
      mimeType
    }
  };
}

/**
 * Interface for resume file input
 */
export interface ResumeFileInput {
  buffer: ArrayBuffer;
  mimeType: string;
}

/**
 * Interface for job image input
 */
export interface JobImageInput {
  buffer: ArrayBuffer;
  mimeType: string;
}

/**
 * Options for structured letter generation
 */
export interface StructuredLetterGenerationOptions {
  templateId?: string;
  language?: 'id' | 'en';
  extractCompanyFromJob?: boolean;
  includeAttachments?: boolean;
}

/**
 * Response schema for structured letter generation
 */
const structuredLetterSchema = {
  type: Type.OBJECT,
  properties: {
    metadata: {
      type: Type.OBJECT,
      properties: {
        generatedAt: { type: Type.STRING },
        lastModified: { type: Type.STRING },
        templateId: { type: Type.STRING },
        language: { type: Type.STRING }
      },
      required: ["generatedAt", "lastModified", "templateId", "language"]
    },
    header: {
      type: Type.OBJECT,
      properties: {
        date: { type: Type.STRING }
      },
      required: ["date"]
    },
    subject: {
      type: Type.OBJECT,
      properties: {
        prefix: { type: Type.STRING },
        position: { type: Type.STRING }
      },
      required: ["prefix", "position"]
    },
    recipient: {
      type: Type.OBJECT,
      properties: {
        salutation: { type: Type.STRING },
        title: { type: Type.STRING },
        company: { type: Type.STRING },
        address: {
          type: Type.ARRAY,
          items: { type: Type.STRING }
        }
      },
      required: ["salutation", "title"]
    },
    body: {
      type: Type.OBJECT,
      properties: {
        opening: { type: Type.STRING },
        paragraphs: {
          type: Type.ARRAY,
          items: { type: Type.STRING }
        },
        closing: { type: Type.STRING }
      },
      required: ["opening", "paragraphs", "closing"]
    },
    signature: {
      type: Type.OBJECT,
      properties: {
        farewell: { type: Type.STRING },
        name: { type: Type.STRING },
        additionalInfo: { type: Type.STRING }
      },
      required: ["farewell", "name"]
    },
    attachments: {
      type: Type.ARRAY,
      items: { type: Type.STRING }
    }
  },
  required: ["metadata", "header", "subject", "recipient", "body", "signature"]
};

/**
 * Generate structured letter data using Google AI
 * This replaces the previous plain text generation with structured JSON output
 */
export async function generateStructuredLetterData(
  resumeFile: ResumeFileInput,
  jobDescription?: string,
  jobImage?: JobImageInput,
  options: StructuredLetterGenerationOptions = {}
): Promise<StructuredLetterData> {
  try {
    // Validate inputs
    if (!['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain', 'image/png', 'image/jpeg', 'image/jpg'].includes(resumeFile.mimeType)) {
      throw new Error('Unsupported resume file format. Please use PDF, DOCX, PNG, JPG, or JPEG files.');
    }

    if (!jobDescription && !jobImage) {
      throw new Error('Either job description text or job posting image must be provided');
    }

    const {
      templateId = 'clean-professional',
      language = 'id',
      extractCompanyFromJob = true,
      includeAttachments = true
    } = options;

    let resumePart: Part;
    let jobImagePart: Part | undefined;

    // Process resume file
    if (resumeFile.mimeType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
      const extractedText = await extractTextFromDocx(resumeFile.buffer);
      resumePart = { text: extractedText };
    } else {
      resumePart = await fileToGenerativePart(resumeFile.buffer, resumeFile.mimeType);
    }

    // Process job image if provided
    if (jobImage) {
      if (!jobImage.mimeType.startsWith('image/')) {
        throw new Error('Unsupported job image format. Only image formats are accepted.');
      }
      jobImagePart = await fileToGenerativePart(jobImage.buffer, jobImage.mimeType);
    }

    // Format current date
    const date = new Date().toLocaleDateString(language === 'id' ? 'id-ID' : 'en-US', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
      timeZone: language === 'id' ? 'Asia/Jakarta' : 'UTC',
    });

    // Create the structured prompt
    const prompt = createStructuredPrompt(date, templateId, language, jobDescription, includeAttachments);

    // Make request to Gemini API
    const response = await googleAI.models.generateContent({
      model: "gemini-2.0-flash",
      contents: [
        {
          role: "user",
          parts: [
            { text: prompt },
            resumePart,
            ...(jobImagePart ? [jobImagePart] : [])
          ]
        }
      ],
      config: {
        temperature: 0.2,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 2048,
        responseMimeType: "application/json",
        responseSchema: structuredLetterSchema,
      },
    });

    const responseText = response.text || '';

    try {
      // Parse the JSON response directly
      const parsedResponse: StructuredLetterData = JSON.parse(responseText);

      // Validate the structured data
      const validation = validateStructuredLetterData(parsedResponse);
      if (!validation.isValid) {
        console.warn('Generated structured data has missing fields:', validation.missingFields);
        // Fill in missing fields with defaults
        const defaultData = createDefaultStructuredLetterData(templateId, language);
        const mergedData = fillMissingFields(parsedResponse, defaultData);
        return validateAndFixStructuredData(mergedData);
      }

      return validateAndFixStructuredData(parsedResponse);
    } catch (parseError) {
      console.error('Error parsing AI response as JSON:', parseError);
      console.error('Response text:', responseText);

      // Fallback to the original parsing method
      const structuredData = parseStructuredResponse(responseText, templateId, language);

      // Validate the structured data
      const validation = validateStructuredLetterData(structuredData);
      if (!validation.isValid) {
        console.warn('Generated structured data has missing fields:', validation.missingFields);
        // Fill in missing fields with defaults
        const defaultData = createDefaultStructuredLetterData(templateId, language);
        const mergedData = fillMissingFields(structuredData, defaultData);
        return validateAndFixStructuredData(mergedData);
      }

      return validateAndFixStructuredData(structuredData);
    }

  } catch (error) {
    console.error('Error generating structured letter data:', error);
    captureApiError('generate-structured-letter-data', error);
    throw new Error('Failed to generate structured letter data');
  }
}

/**
 * Create the structured prompt for AI generation
 */
function createStructuredPrompt(
  date: string,
  templateId: string,
  language: 'id' | 'en',
  jobDescription?: string,
  includeAttachments: boolean = true
): string {
  const isIndonesian = language === 'id';
  
  const baseStructure = isIndonesian ? {
    opening: 'Dengan hormat,',
    farewell: 'Hormat saya,',
    subjectPrefix: 'Perihal: Lamaran Pekerjaan sebagai',
    salutation: 'Yth.',
    recipientTitle: 'Bapak/Ibu Bagian Sumber Daya Manusia',
    closing: 'Atas perhatian dan waktu yang Bapak/Ibu berikan, saya ucapkan terima kasih.',
    attachments: ['Curriculum Vitae', 'Portofolio']
  } : {
    opening: 'Dear Sir/Madam,',
    farewell: 'Sincerely,',
    subjectPrefix: 'Subject: Job Application for',
    salutation: 'Dear',
    recipientTitle: 'Hiring Manager',
    closing: 'Thank you for your time and consideration.',
    attachments: ['Resume', 'Portfolio']
  };

  return `
You are a professional job application letter writer. Generate a structured JSON object for a formal job application letter ${isIndonesian ? 'in Bahasa Indonesia' : 'in English'}.

First, analyze the resume and job description thoroughly to identify:
1. Skills EXPLICITLY mentioned in the resume that match the job requirements
2. Skills IMPLICITLY suggested by the resume
3. Related skills in the resume that could TRANSFER to the job requirements
4. Education, certifications, and relevant experience from the resume
5. Company name and position title from the job information

Generate a JSON object with this EXACT structure:
{
  "metadata": {
    "generatedAt": "${new Date().toISOString()}",
    "lastModified": "${new Date().toISOString()}",
    "templateId": "${templateId}",
    "language": "${language}"
  },
  "header": {
    "date": "${date}"
  },
  "subject": {
    "prefix": "${baseStructure.subjectPrefix}",
    "position": "[Extract exact position title from job description]"
  },
  "recipient": {
    "salutation": "${baseStructure.salutation}",
    "title": "${baseStructure.recipientTitle}",
    "company": "[Extract company name if available]",
    "address": "[Extract company address as array if available, or omit if not found]"
  },
  "body": {
    "opening": "${baseStructure.opening}",
    "paragraphs": [
      "[First paragraph: State purpose and position applied for clearly]",
      "[Second paragraph: Highlight relevant education, skills, and experiences that match job requirements]",
      "[Third paragraph: Show enthusiasm, mention company specifically if possible, and express desire to contribute]"
    ],
    "closing": "${baseStructure.closing}"
  },
  "signature": {
    "farewell": "${baseStructure.farewell}",
    "name": "[Extract candidate name from resume]",
    "additionalInfo": "[Optional: contact info if appropriate]"
  }${includeAttachments ? `,
  "attachments": ${JSON.stringify(baseStructure.attachments)}` : ''}
}

Job Information: ${jobDescription || '[A job posting image is provided. Analyze it to identify job requirements, responsibilities, qualifications, company name, and position title]'}

CRITICAL REQUIREMENTS:
1. The total character count of all paragraphs combined should be 1300-1600 characters
2. Extract the EXACT position title and company name from the job information
3. NEVER mention lack of experience or use negative phrases
4. Focus ONLY on candidate strengths and relevant experience
5. Generate exactly 3 substantial paragraphs for the body
6. Use ${isIndonesian ? 'formal Indonesian business correspondence' : 'professional English business'} style
7. If company address is not clearly specified, omit the address field entirely
8. Extract candidate name accurately from the resume

Return ONLY the JSON object without any markdown formatting, code blocks, or additional text.
  `;
}

/**
 * Parse the structured response from AI
 */
function parseStructuredResponse(
  responseText: string,
  templateId: string,
  language: 'id' | 'en'
): StructuredLetterData {
  try {
    // Try to parse directly as JSON
    let parsedData;
    try {
      parsedData = JSON.parse(responseText.trim());
    } catch (parseError) {
      // Try to extract JSON from markdown code blocks
      const jsonMatch = responseText.match(/```json\s*([\s\S]*?)\s*```/);
      if (jsonMatch) {
        parsedData = JSON.parse(jsonMatch[1]);
      } else {
        // Try to find JSON object in the text
        const jsonStart = responseText.indexOf('{');
        const jsonEnd = responseText.lastIndexOf('}');
        if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
          const jsonStr = responseText.substring(jsonStart, jsonEnd + 1);
          parsedData = JSON.parse(jsonStr);
        } else {
          throw new Error('Could not extract valid JSON from response');
        }
      }
    }

    // Ensure required fields are present
    if (!parsedData.metadata) {
      parsedData.metadata = {
        generatedAt: new Date().toISOString(),
        lastModified: new Date().toISOString(),
        templateId,
        language
      };
    }

    return parsedData as StructuredLetterData;

  } catch (error) {
    console.error('Failed to parse structured response:', error);
    console.error('Response text:', responseText);
    throw new Error('Invalid structured data response from AI');
  }
}

/**
 * Fill missing fields in structured data with defaults
 */
function fillMissingFields(
  incomplete: Partial<StructuredLetterData>,
  defaults: StructuredLetterData
): StructuredLetterData {
  return {
    metadata: {
      ...defaults.metadata,
      ...incomplete.metadata
    },
    header: {
      ...defaults.header,
      ...incomplete.header
    },
    subject: {
      ...defaults.subject,
      ...incomplete.subject
    },
    recipient: {
      ...defaults.recipient,
      ...incomplete.recipient
    },
    body: {
      ...defaults.body,
      ...incomplete.body,
      paragraphs: incomplete.body?.paragraphs?.length ? incomplete.body.paragraphs : defaults.body.paragraphs
    },
    signature: {
      ...defaults.signature,
      ...incomplete.signature
    },
    attachments: incomplete.attachments || defaults.attachments
  };
}

/**
 * Validate and fix common issues in generated structured data
 */
export function validateAndFixStructuredData(data: StructuredLetterData): StructuredLetterData {
  const fixed = { ...data };

  // Ensure paragraphs are not empty
  if (!fixed.body.paragraphs || fixed.body.paragraphs.length === 0) {
    fixed.body.paragraphs = ['[Paragraph content needs to be generated]'];
  }

  // Ensure position is not empty
  if (!fixed.subject.position || fixed.subject.position.trim() === '') {
    fixed.subject.position = '[Position Title]';
  }

  // Ensure candidate name is not empty
  if (!fixed.signature.name || fixed.signature.name.trim() === '') {
    fixed.signature.name = '[Candidate Name]';
  }

  // Clean up any placeholder text in company field
  if (fixed.recipient.company && (
    fixed.recipient.company.includes('[') || 
    fixed.recipient.company.toLowerCase().includes('extract') ||
    fixed.recipient.company.toLowerCase().includes('company name')
  )) {
    delete fixed.recipient.company;
  }

  // Clean up address field
  if (fixed.recipient.address && Array.isArray(fixed.recipient.address)) {
    const cleanAddresses = fixed.recipient.address.filter(addr => 
      addr && 
      !addr.includes('[') && 
      !addr.toLowerCase().includes('extract') &&
      !addr.toLowerCase().includes('address')
    );
    if (cleanAddresses.length === 0) {
      delete fixed.recipient.address;
    } else {
      fixed.recipient.address = cleanAddresses;
    }
  }

  return fixed;
}